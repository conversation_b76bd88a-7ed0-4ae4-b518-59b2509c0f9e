import {motion} from "framer-motion";
import {useInView} from "framer-motion";
import {useRef} from "react";

const About = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {once: true, threshold: 0.3});

  const containerVariants = {
    hidden: {opacity: 0},
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {y: 50, opacity: 0},
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section
      id="about"
      className="py-24 px-4 sm:px-6 lg:px-8 bg-section-bg relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(56,189,248,0.05),transparent_50%),radial-gradient(circle_at_70%_80%,rgba(20,184,166,0.05),transparent_50%)]"></div>

      <div className="max-w-7xl mx-auto relative z-10">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-20">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-sky-blue/10 border border-sky-blue/30 rounded-full text-sky-blue text-sm font-medium mb-6">
              <div className="w-2 h-2 bg-sky-blue rounded-full"></div>
              Get to know me
            </div>
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-heading font-bold text-light-text mb-6">
              About{" "}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-sky-blue to-teal-accent">
                Me
              </span>
            </h2>
            <p className="text-xl text-light-text/70 max-w-3xl mx-auto leading-relaxed">
              I'm a passionate developer who loves turning complex problems into
              simple, beautiful solutions. Here's my story.
            </p>
          </motion.div>

          {/* Main Content */}
          <div className="grid lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Visual */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-1 flex justify-center"
            >
              <div className="relative">
                {/* Main Avatar Container */}
                <div className="relative w-72 h-72 rounded-3xl bg-gradient-to-br from-sky-blue/10 to-teal-accent/10 border border-sky-blue/20 backdrop-blur-sm overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-sky-blue/5 to-teal-accent/5"></div>
                  <div className="relative z-10 h-full flex items-center justify-center">
                    <div className="text-8xl">👨‍💻</div>
                  </div>

                  {/* Decorative Elements */}
                  <div className="absolute top-4 right-4 w-3 h-3 bg-sky-blue rounded-full animate-pulse"></div>
                  <div className="absolute bottom-4 left-4 w-2 h-2 bg-teal-accent rounded-full animate-pulse delay-1000"></div>
                </div>

                {/* Floating Tech Badges */}
                <motion.div
                  className="absolute -top-6 -right-6 px-3 py-2 bg-dark-navy/80 border border-sky-blue/30 rounded-lg backdrop-blur-sm"
                  animate={{y: [-5, 5, -5]}}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  <span className="text-sky-blue text-sm font-medium">
                    React
                  </span>
                </motion.div>

                <motion.div
                  className="absolute -bottom-6 -left-6 px-3 py-2 bg-dark-navy/80 border border-teal-accent/30 rounded-lg backdrop-blur-sm"
                  animate={{y: [5, -5, 5]}}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1,
                  }}
                >
                  <span className="text-teal-accent text-sm font-medium">
                    Node.js
                  </span>
                </motion.div>
              </div>
            </motion.div>

            {/* Text Content */}
            <motion.div variants={itemVariants} className="text-left space-y-6">
              {/* Bio */}
              <div>
                <h3 className="text-2xl font-heading font-semibold text-sky-blue mb-4">
                  Full-Stack Developer
                </h3>
                <p className="text-lg text-light-text/80 leading-relaxed mb-6">
                  I'm a passionate full-stack developer specializing in the MERN
                  stack. I love building elegant, fast, and user-focused web
                  experiences with modern tools and technologies.
                </p>
                <p className="text-lg text-light-text/80 leading-relaxed">
                  With a keen eye for design and a strong foundation in both
                  frontend and backend development, I create seamless digital
                  experiences that not only look great but perform exceptionally
                  well.
                </p>
              </div>

              {/* Fun Fact */}
              <motion.div
                className="bg-section-bg/50 border border-sky-blue/20 rounded-xl p-6"
                whileHover={{scale: 1.02}}
                transition={{duration: 0.2}}
              >
                <div className="flex items-start gap-4">
                  <div className="text-2xl">💡</div>
                  <div>
                    <h4 className="font-heading font-semibold text-teal-accent mb-2">
                      Fun Fact
                    </h4>
                    <p className="text-light-text/80">
                      I believe a good UI is 80% of UX. The remaining 20% is
                      making sure it actually works! ✨
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 gap-4 pt-4">
                <motion.div
                  className="text-center p-4 bg-sky-blue/10 rounded-lg border border-sky-blue/20"
                  whileHover={{y: -5}}
                  transition={{duration: 0.2}}
                >
                  <div className="text-2xl font-heading font-bold text-sky-blue">
                    3+
                  </div>
                  <div className="text-sm text-light-text/70">
                    Years Experience
                  </div>
                </motion.div>

                <motion.div
                  className="text-center p-4 bg-teal-accent/10 rounded-lg border border-teal-accent/20"
                  whileHover={{y: -5}}
                  transition={{duration: 0.2}}
                >
                  <div className="text-2xl font-heading font-bold text-teal-accent">
                    50+
                  </div>
                  <div className="text-sm text-light-text/70">
                    Projects Built
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>

          {/* Values/Principles */}
          <motion.div
            variants={itemVariants}
            className="mt-16 grid md:grid-cols-3 gap-8"
          >
            {[
              {
                icon: "🎯",
                title: "User-Focused",
                description:
                  "Every line of code is written with the end user in mind",
              },
              {
                icon: "⚡",
                title: "Performance",
                description:
                  "Fast, optimized, and scalable solutions that deliver results",
              },
              {
                icon: "🔧",
                title: "Clean Code",
                description:
                  "Maintainable, readable, and well-documented code practices",
              },
            ].map((value, index) => (
              <motion.div
                key={index}
                className="bg-section-bg/30 border border-sky-blue/10 rounded-xl p-6 text-center"
                whileHover={{y: -10, scale: 1.05}}
                transition={{duration: 0.3}}
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h4 className="font-heading font-semibold text-light-text mb-2">
                  {value.title}
                </h4>
                <p className="text-light-text/70 text-sm">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
