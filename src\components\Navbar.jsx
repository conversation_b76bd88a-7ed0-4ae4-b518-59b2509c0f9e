import {useState, useEffect} from "react";
import {motion} from "framer-motion";

const Navbar = () => {
  const [activeSection, setActiveSection] = useState("home");
  const [scrollProgress, setScrollProgress] = useState(0);

  const navItems = [
    {id: "home", label: "Home"},
    {id: "about", label: "About"},
    {id: "skills", label: "Skills"},
    {id: "projects", label: "Projects"},
    {id: "resume", label: "Resume"},
    {id: "contact", label: "Contact"},
  ];

  useEffect(() => {
    const handleScroll = () => {
      // Calculate scroll progress
      const totalHeight =
        document.documentElement.scrollHeight - window.innerHeight;
      const progress = (window.scrollY / totalHeight) * 100;
      setScrollProgress(progress);

      // Update active section based on scroll position
      const sections = navItems.map((item) => document.getElementById(item.id));
      const scrollPosition = window.scrollY + 100;

      sections.forEach((section, index) => {
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;

          if (
            scrollPosition >= sectionTop &&
            scrollPosition < sectionTop + sectionHeight
          ) {
            setActiveSection(navItems[index].id);
          }
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({behavior: "smooth"});
    }
  };

  return (
    <>
      {/* Scroll Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-section-bg z-50">
        <motion.div
          className="h-full bg-gradient-to-r from-sky-blue to-teal-accent"
          style={{width: `${scrollProgress}%`}}
          initial={{width: 0}}
          animate={{width: `${scrollProgress}%`}}
          transition={{duration: 0.1}}
        />
      </div>

      {/* Navigation */}
      <motion.nav
        className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40"
        initial={{y: -100}}
        animate={{y: 0}}
        transition={{duration: 0.6}}
      >
        <div className="bg-dark-navy/90 backdrop-blur-xl border border-sky-blue/20 rounded-2xl shadow-2xl shadow-sky-blue/10 px-8 py-2">
          <div className="flex items-center justify-center h-16">
            {/* Navigation Links - Centered */}
            <div className="hidden md:flex items-center gap-16">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`relative px-4 py-3 text-base font-medium transition-colors duration-200 ${
                    activeSection === item.id
                      ? "text-sky-blue"
                      : "text-light-text hover:text-sky-blue"
                  }`}
                >
                  {item.label}
                  {activeSection === item.id && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-sky-blue"
                      layoutId="activeTab"
                      initial={false}
                      transition={{type: "spring", stiffness: 500, damping: 30}}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      </motion.nav>
    </>
  );
};

export default Navbar;
