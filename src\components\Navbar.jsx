import {useState, useEffect} from "react";
import {motion} from "framer-motion";

const Navbar = () => {
  const [activeSection, setActiveSection] = useState("home");
  const [scrollProgress, setScrollProgress] = useState(0);
  const [darkMode, setDarkMode] = useState(true);

  const navItems = [
    {id: "home", label: "Home"},
    {id: "about", label: "About"},
    {id: "skills", label: "Skills"},
    {id: "projects", label: "Projects"},
    {id: "resume", label: "Resume"},
    {id: "contact", label: "Contact"},
  ];

  useEffect(() => {
    const handleScroll = () => {
      // Calculate scroll progress
      const totalHeight =
        document.documentElement.scrollHeight - window.innerHeight;
      const progress = (window.scrollY / totalHeight) * 100;
      setScrollProgress(progress);

      // Update active section based on scroll position
      const sections = navItems.map((item) => document.getElementById(item.id));
      const scrollPosition = window.scrollY + 100;

      sections.forEach((section, index) => {
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;

          if (
            scrollPosition >= sectionTop &&
            scrollPosition < sectionTop + sectionHeight
          ) {
            setActiveSection(navItems[index].id);
          }
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({behavior: "smooth"});
    }
  };

  return (
    <>
      {/* Scroll Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-section-bg z-50">
        <motion.div
          className="h-full bg-gradient-to-r from-sky-blue to-teal-accent"
          style={{width: `${scrollProgress}%`}}
          initial={{width: 0}}
          animate={{width: `${scrollProgress}%`}}
          transition={{duration: 0.1}}
        />
      </div>

      {/* Navigation */}
      <motion.nav
        className="fixed top-1 left-0 right-0 z-40 bg-dark-navy/80 backdrop-blur-md border-b border-section-bg"
        initial={{y: -100}}
        animate={{y: 0}}
        transition={{duration: 0.6}}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 relative">
            {/* Left spacer for balance */}
            <div className="w-16"></div>

            {/* Navigation Links - Centered */}
            <div className="hidden md:flex items-center gap-16">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`relative px-4 py-3 text-base font-medium transition-colors duration-200 ${
                    activeSection === item.id
                      ? "text-sky-blue"
                      : "text-light-text hover:text-sky-blue"
                  }`}
                >
                  {item.label}
                  {activeSection === item.id && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-sky-blue"
                      layoutId="activeTab"
                      initial={false}
                      transition={{type: "spring", stiffness: 500, damping: 30}}
                    />
                  )}
                </button>
              ))}
            </div>

            {/* Dark Mode Toggle - Right Side */}
            <motion.button
              onClick={() => setDarkMode(!darkMode)}
              className="p-3 rounded-xl bg-section-bg/80 border border-sky-blue/20 hover:bg-sky-blue/10 hover:border-sky-blue/40 transition-all duration-200 backdrop-blur-sm"
              whileHover={{scale: 1.05}}
              whileTap={{scale: 0.95}}
            >
              <div className="w-5 h-5 text-sky-blue">
                {darkMode ? "🌙" : "☀️"}
              </div>
            </motion.button>
          </div>
        </div>
      </motion.nav>
    </>
  );
};

export default Navbar;
