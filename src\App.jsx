import {useState} from "react";
import Navbar from "./components/Navbar";
import Hero from "./components/Hero";
import About from "./components/About";
import Skills from "./components/Skills";
import Projects from "./components/Projects";
import Resume from "./components/Resume";
import Contact from "./components/Contact";
import Footer from "./components/Footer";
import ContactModal from "./components/ContactModal";
import CustomCursor from "./components/CustomCursor";

function App() {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  const handleContactClick = () => {
    setIsContactModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsContactModalOpen(false);
  };

  return (
    <div className="min-h-screen bg-dark-navy cursor-none">
      <CustomCursor />
      <Navbar />
      <Hero onContactClick={handleContactClick} />
      <ContactModal isOpen={isContactModalOpen} onClose={handleCloseModal} />

      <About />
      <Skills />
      <Projects />

      <Resume />
      <Contact />
      <Footer />
    </div>
  );
}

export default App;
