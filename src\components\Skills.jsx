import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { 
  FaHtml5, 
  FaCss3Alt, 
  FaJs, 
  FaReact, 
  FaNodeJs, 
  FaGitAlt, 
  FaGithub 
} from 'react-icons/fa';
import { 
  SiExpress, 
  SiMongodb, 
  SiMysql, 
  SiPostman, 
  SiCanva, 
  SiAdobephotoshop,
  SiTailwindcss,
  SiVite
} from 'react-icons/si';

const Skills = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.2 });

  const skillCategories = [
    {
      title: "Frontend",
      color: "sky-blue",
      skills: [
        { name: "HTML5", icon: FaHtml5, level: 95 },
        { name: "CSS3", icon: FaCss3Alt, level: 90 },
        { name: "JavaScript", icon: FaJs, level: 88 },
        { name: "React", icon: FaReact, level: 85 },
        { name: "Tailwind CSS", icon: SiT<PERSON>windcss, level: 90 },
        { name: "Vite", icon: SiVite, level: 80 },
      ]
    },
    {
      title: "Backend",
      color: "teal-accent",
      skills: [
        { name: "Node.js", icon: FaNodeJs, level: 82 },
        { name: "Express", icon: SiExpress, level: 80 },
      ]
    },
    {
      title: "Databases",
      color: "sky-blue",
      skills: [
        { name: "MongoDB", icon: SiMongodb, level: 78 },
        { name: "MySQL", icon: SiMysql, level: 75 },
      ]
    },
    {
      title: "Tools",
      color: "teal-accent",
      skills: [
        { name: "Git", icon: FaGitAlt, level: 85 },
        { name: "GitHub", icon: FaGithub, level: 88 },
        { name: "Postman", icon: SiPostman, level: 80 },
      ]
    },
    {
      title: "Design",
      color: "sky-blue",
      skills: [
        { name: "Canva", icon: SiCanva, level: 85 },
        { name: "Photoshop", icon: SiAdobephotoshop, level: 70 },
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const categoryVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const skillVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="skills" className="py-20 px-4 sm:px-6 lg:px-8 bg-section-bg">
      <div className="max-w-7xl mx-auto">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Title */}
          <motion.div variants={categoryVariants} className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-heading font-bold text-light-text mb-4">
              My{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-sky-blue to-teal-accent">
                Skills
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-sky-blue to-teal-accent mx-auto rounded-full mb-6"></div>
            <p className="text-lg text-light-text/70 max-w-2xl mx-auto">
              Here are the technologies and tools I work with to bring ideas to life
            </p>
          </motion.div>

          {/* Skills Grid */}
          <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {skillCategories.map((category, categoryIndex) => (
              <motion.div
                key={category.title}
                variants={categoryVariants}
                className="bg-dark-navy/50 border border-sky-blue/20 rounded-2xl p-6 backdrop-blur-sm"
                whileHover={{ y: -5, scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                {/* Category Header */}
                <div className="mb-6">
                  <h3 className={`text-xl font-heading font-semibold mb-2 ${
                    category.color === 'sky-blue' ? 'text-sky-blue' : 'text-teal-accent'
                  }`}>
                    {category.title}
                  </h3>
                  <div className={`w-12 h-1 rounded-full ${
                    category.color === 'sky-blue' ? 'bg-sky-blue' : 'bg-teal-accent'
                  }`}></div>
                </div>

                {/* Skills List */}
                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <motion.div
                      key={skill.name}
                      variants={skillVariants}
                      className="group"
                      initial="hidden"
                      animate={isInView ? "visible" : "hidden"}
                      transition={{ delay: categoryIndex * 0.1 + skillIndex * 0.05 }}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-3">
                          <skill.icon className={`text-xl ${
                            category.color === 'sky-blue' ? 'text-sky-blue' : 'text-teal-accent'
                          } group-hover:scale-110 transition-transform duration-200`} />
                          <span className="text-light-text font-medium">
                            {skill.name}
                          </span>
                        </div>
                        <span className="text-sm text-light-text/60">
                          {skill.level}%
                        </span>
                      </div>
                      
                      {/* Progress Bar */}
                      <div className="w-full bg-dark-navy rounded-full h-2 overflow-hidden">
                        <motion.div
                          className={`h-full rounded-full ${
                            category.color === 'sky-blue' 
                              ? 'bg-gradient-to-r from-sky-blue to-sky-blue/80' 
                              : 'bg-gradient-to-r from-teal-accent to-teal-accent/80'
                          }`}
                          initial={{ width: 0 }}
                          animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}
                          transition={{ 
                            duration: 1.5, 
                            delay: categoryIndex * 0.2 + skillIndex * 0.1,
                            ease: "easeOut"
                          }}
                        />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Additional Info */}
          <motion.div
            variants={categoryVariants}
            className="mt-16 text-center"
          >
            <div className="bg-dark-navy/30 border border-sky-blue/10 rounded-2xl p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-heading font-semibold text-light-text mb-4">
                Always Learning
              </h3>
              <p className="text-light-text/70 mb-6">
                Technology evolves rapidly, and so do I. I'm constantly exploring new tools, 
                frameworks, and best practices to stay at the forefront of web development.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                {["TypeScript", "Next.js", "GraphQL", "Docker", "AWS"].map((tech, index) => (
                  <motion.span
                    key={tech}
                    className="px-4 py-2 bg-sky-blue/10 border border-sky-blue/30 rounded-full text-sky-blue text-sm font-medium"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(56, 189, 248, 0.2)" }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                    transition={{ delay: 1 + index * 0.1 }}
                  >
                    {tech}
                  </motion.span>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Skills;
