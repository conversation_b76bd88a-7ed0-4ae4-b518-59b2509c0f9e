import {motion} from "framer-motion";

const Hero = ({onContactClick}) => {
  const scrollToProjects = () => {
    const element = document.getElementById("projects");
    if (element) {
      element.scrollIntoView({behavior: "smooth"});
    }
  };

  const containerVariants = {
    hidden: {opacity: 0},
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {y: 30, opacity: 0},
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const buttonVariants = {
    hidden: {scale: 0.8, opacity: 0},
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
      },
    },
    tap: {
      scale: 0.95,
    },
  };

  return (
    <section
      id="home"
      className="min-h-screen relative overflow-hidden bg-dark-navy"
    >
      {/* Background Grid <PERSON>tern */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(56,189,248,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(56,189,248,0.03)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

      {/* Animated Background Elements */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-sky-blue/20 to-teal-accent/20 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.3, 1],
          rotate: [0, 180, 360],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-l from-teal-accent/15 to-sky-blue/15 rounded-full blur-3xl"
        animate={{
          scale: [1.2, 0.8, 1.2],
          rotate: [360, 180, 0],
          opacity: [0.2, 0.5, 0.2],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Main Content Container */}
      <div className="relative z-10 min-h-screen flex items-center justify-center">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <motion.div
            className="grid lg:grid-cols-2 gap-12 items-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Left Column - Text Content */}
            <div className="space-y-8 text-left">
              {/* Greeting Badge */}
              <motion.div
                variants={itemVariants}
                className="inline-flex items-center gap-2 px-4 py-2 bg-sky-blue/10 border border-sky-blue/30 rounded-full text-sky-blue text-sm font-medium"
              >
                <div className="w-2 h-2 bg-sky-blue rounded-full animate-pulse"></div>
                Available for new opportunities
              </motion.div>

              {/* Main Headline */}
              <motion.div variants={itemVariants} className="space-y-4">
                <h1 className="font-heading text-5xl sm:text-6xl lg:text-7xl font-bold leading-tight">
                  <span className="text-light-text">Hi, I'm </span>
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-sky-blue via-teal-accent to-sky-blue bg-size-200 animate-gradient">
                    Yadav Rana
                  </span>
                </h1>
                <div className="w-24 h-1 bg-gradient-to-r from-sky-blue to-teal-accent rounded-full"></div>
              </motion.div>

              {/* Role & Description */}
              <motion.div variants={itemVariants} className="space-y-4">
                <h2 className="text-2xl sm:text-3xl font-heading font-semibold text-sky-blue">
                  Full-Stack Developer & UI/UX Enthusiast
                </h2>
                <p className="text-lg text-light-text/80 leading-relaxed max-w-xl">
                  I craft digital experiences that blend beautiful design with
                  powerful functionality. Specializing in the MERN stack and
                  modern web technologies.
                </p>
              </motion.div>

              {/* Stats */}
              <motion.div variants={itemVariants} className="flex gap-8">
                <div className="text-center">
                  <div className="text-3xl font-heading font-bold text-sky-blue">
                    3+
                  </div>
                  <div className="text-sm text-light-text/60">
                    Years Experience
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-heading font-bold text-teal-accent">
                    50+
                  </div>
                  <div className="text-sm text-light-text/60">
                    Projects Completed
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-heading font-bold text-sky-blue">
                    100%
                  </div>
                  <div className="text-sm text-light-text/60">
                    Client Satisfaction
                  </div>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                variants={itemVariants}
                className="flex flex-col sm:flex-row gap-4"
              >
                <motion.button
                  onClick={onContactClick}
                  className="group px-8 py-4 bg-gradient-to-r from-sky-blue to-teal-accent text-dark-navy font-semibold rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 relative overflow-hidden"
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap"
                >
                  <span className="relative z-10">Let's Work Together</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-teal-accent to-sky-blue opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </motion.button>

                <motion.button
                  onClick={scrollToProjects}
                  className="px-8 py-4 border-2 border-sky-blue/50 text-sky-blue font-semibold rounded-xl hover:bg-sky-blue/10 hover:border-sky-blue transition-all duration-300 backdrop-blur-sm"
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap"
                >
                  View My Work
                </motion.button>
              </motion.div>
            </div>

            {/* Right Column - Visual Element */}
            <motion.div
              variants={itemVariants}
              className="relative flex items-center justify-center lg:justify-end"
            >
              {/* Main Visual Container */}
              <div className="relative w-80 h-80 lg:w-96 lg:h-96">
                {/* Outer Ring */}
                <motion.div
                  className="absolute inset-0 border-2 border-sky-blue/30 rounded-full"
                  animate={{rotate: 360}}
                  transition={{duration: 20, repeat: Infinity, ease: "linear"}}
                />

                {/* Middle Ring */}
                <motion.div
                  className="absolute inset-8 border border-teal-accent/40 rounded-full"
                  animate={{rotate: -360}}
                  transition={{duration: 15, repeat: Infinity, ease: "linear"}}
                />

                {/* Inner Circle */}
                <div className="absolute inset-16 bg-gradient-to-br from-sky-blue/20 to-teal-accent/20 rounded-full backdrop-blur-sm border border-sky-blue/20 flex items-center justify-center">
                  <div className="text-6xl lg:text-7xl">👨‍💻</div>
                </div>

                {/* Floating Tech Icons */}
                <motion.div
                  className="absolute -top-4 left-1/4 w-12 h-12 bg-sky-blue/20 rounded-lg flex items-center justify-center border border-sky-blue/30 backdrop-blur-sm"
                  animate={{y: [-10, 10, -10]}}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  <span className="text-xl">⚛️</span>
                </motion.div>

                <motion.div
                  className="absolute top-1/4 -right-4 w-10 h-10 bg-teal-accent/20 rounded-lg flex items-center justify-center border border-teal-accent/30 backdrop-blur-sm"
                  animate={{y: [10, -10, 10]}}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1,
                  }}
                >
                  <span className="text-lg">🚀</span>
                </motion.div>

                <motion.div
                  className="absolute bottom-1/4 -left-4 w-11 h-11 bg-sky-blue/20 rounded-lg flex items-center justify-center border border-sky-blue/30 backdrop-blur-sm"
                  animate={{y: [-8, 8, -8]}}
                  transition={{
                    duration: 3.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5,
                  }}
                >
                  <span className="text-lg">💡</span>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        initial={{opacity: 0, y: 20}}
        animate={{opacity: 1, y: 0}}
        transition={{delay: 2, duration: 0.8}}
      >
        <motion.div
          className="flex flex-col items-center gap-2 text-sky-blue/60"
          animate={{y: [0, 8, 0]}}
          transition={{duration: 2, repeat: Infinity, ease: "easeInOut"}}
        >
          <span className="text-xs font-medium">Scroll to explore</span>
          <div className="w-6 h-10 border-2 border-sky-blue/40 rounded-full flex justify-center">
            <motion.div
              className="w-1 h-3 bg-sky-blue/60 rounded-full mt-2"
              animate={{opacity: [1, 0.3, 1]}}
              transition={{duration: 2, repeat: Infinity}}
            />
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;
