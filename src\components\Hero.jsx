import { motion } from 'framer-motion';

const Hero = ({ onContactClick }) => {
  const scrollToProjects = () => {
    const element = document.getElementById('projects');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const buttonVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
      },
    },
    tap: {
      scale: 0.95,
    },
  };

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-navy via-section-bg to-dark-navy"></div>
      
      {/* Animated Background Shapes */}
      <motion.div
        className="absolute top-20 left-10 w-72 h-72 bg-sky-blue/10 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute bottom-20 right-10 w-96 h-96 bg-teal-accent/10 rounded-full blur-3xl"
        animate={{
          scale: [1.2, 1, 1.2],
          opacity: [0.2, 0.4, 0.2],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Main Content */}
      <motion.div
        className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Greeting */}
        <motion.div
          className="mb-6"
          variants={itemVariants}
        >
          <span className="text-sky-blue text-lg font-medium">Hello, I'm</span>
        </motion.div>

        {/* Main Headline */}
        <motion.h1
          className="font-heading text-5xl sm:text-6xl lg:text-7xl font-bold mb-6"
          variants={itemVariants}
        >
          <span className="text-light-text">Yadav</span>{' '}
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-sky-blue to-teal-accent">
            Rana
          </span>
        </motion.h1>

        {/* Subheading */}
        <motion.p
          className="text-xl sm:text-2xl text-light-text/80 mb-8 max-w-2xl mx-auto"
          variants={itemVariants}
        >
          <span className="text-sky-blue font-semibold">MERN Stack Developer</span>
          <br />
          Building Interactive Web Experiences
        </motion.p>

        {/* Description */}
        <motion.p
          className="text-lg text-light-text/70 mb-12 max-w-xl mx-auto"
          variants={itemVariants}
        >
          Passionate about creating elegant, fast, and user-focused web applications 
          with modern technologies and clean code.
        </motion.p>

        {/* Action Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          variants={itemVariants}
        >
          <motion.button
            onClick={onContactClick}
            className="px-8 py-4 bg-gradient-to-r from-sky-blue to-teal-accent text-dark-navy font-semibold rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            Let's Talk
          </motion.button>

          <motion.button
            onClick={scrollToProjects}
            className="px-8 py-4 border-2 border-sky-blue text-sky-blue font-semibold rounded-lg hover:bg-sky-blue hover:text-dark-navy transition-all duration-300"
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            View Projects
          </motion.button>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2, duration: 0.8 }}
        >
          <motion.div
            className="w-6 h-10 border-2 border-sky-blue rounded-full flex justify-center"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <motion.div
              className="w-1 h-3 bg-sky-blue rounded-full mt-2"
              animate={{ opacity: [1, 0, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;
