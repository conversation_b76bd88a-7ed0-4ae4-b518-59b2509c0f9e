import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { FaDownload, FaCalendarAlt, FaMapMarkerAlt, FaGraduationCap, FaBriefcase } from 'react-icons/fa';

const Resume = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.2 });

  const experiences = [
    {
      title: "Full Stack Developer",
      company: "Tech Solutions Inc.",
      location: "Remote",
      period: "2022 - Present",
      description: "Developed and maintained multiple web applications using MERN stack. Led a team of 3 developers and improved application performance by 40%.",
      achievements: [
        "Built 5+ full-stack applications serving 10,000+ users",
        "Implemented CI/CD pipelines reducing deployment time by 60%",
        "Mentored junior developers and conducted code reviews"
      ]
    },
    {
      title: "Frontend Developer",
      company: "Digital Agency Pro",
      location: "New York, NY",
      period: "2021 - 2022",
      description: "Specialized in creating responsive and interactive user interfaces using React and modern CSS frameworks.",
      achievements: [
        "Converted 20+ designs to pixel-perfect responsive websites",
        "Improved website loading speed by 35% through optimization",
        "Collaborated with UX/UI designers on user experience improvements"
      ]
    },
    {
      title: "Junior Web Developer",
      company: "StartUp Hub",
      location: "San Francisco, CA",
      period: "2020 - 2021",
      description: "Started my professional journey building websites and learning modern web development practices.",
      achievements: [
        "Developed 10+ landing pages with high conversion rates",
        "Learned and implemented modern JavaScript frameworks",
        "Participated in agile development processes"
      ]
    }
  ];

  const education = [
    {
      degree: "Bachelor of Computer Science",
      institution: "University of Technology",
      location: "California, USA",
      period: "2018 - 2022",
      gpa: "3.8/4.0",
      relevant: ["Data Structures & Algorithms", "Web Development", "Database Systems", "Software Engineering"]
    },
    {
      degree: "Full Stack Web Development Bootcamp",
      institution: "Code Academy Pro",
      location: "Online",
      period: "2020",
      gpa: "Completed with Distinction",
      relevant: ["MERN Stack", "RESTful APIs", "Database Design", "Version Control"]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const handleDownloadResume = () => {
    // In a real application, this would download the actual resume file
    const link = document.createElement('a');
    link.href = '/resume-yadav-rana.pdf'; // You would place your actual resume PDF in the public folder
    link.download = 'Yadav_Rana_Resume.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section id="resume" className="py-20 px-4 sm:px-6 lg:px-8 bg-section-bg">
      <div className="max-w-7xl mx-auto">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Title */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-heading font-bold text-light-text mb-4">
              My{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-sky-blue to-teal-accent">
                Resume
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-sky-blue to-teal-accent mx-auto rounded-full mb-6"></div>
            <p className="text-lg text-light-text/70 max-w-2xl mx-auto mb-8">
              My professional journey and educational background
            </p>
            
            {/* Download Button */}
            <motion.button
              onClick={handleDownloadResume}
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-sky-blue to-teal-accent text-dark-navy font-semibold rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FaDownload className="w-5 h-5" />
              Download Resume
            </motion.button>
          </motion.div>

          {/* Experience & Education Grid */}
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Experience Section */}
            <motion.div variants={itemVariants}>
              <div className="flex items-center gap-3 mb-8">
                <FaBriefcase className="text-2xl text-sky-blue" />
                <h3 className="text-2xl font-heading font-bold text-light-text">
                  Experience
                </h3>
              </div>

              <div className="space-y-8">
                {experiences.map((exp, index) => (
                  <motion.div
                    key={index}
                    className="relative bg-dark-navy/50 border border-sky-blue/20 rounded-xl p-6"
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* Timeline Dot */}
                    <div className="absolute -left-3 top-6 w-6 h-6 bg-sky-blue rounded-full border-4 border-section-bg"></div>
                    
                    {/* Job Title & Company */}
                    <div className="mb-3">
                      <h4 className="text-xl font-heading font-semibold text-light-text mb-1">
                        {exp.title}
                      </h4>
                      <div className="text-sky-blue font-medium mb-2">{exp.company}</div>
                      <div className="flex flex-wrap gap-4 text-sm text-light-text/60">
                        <div className="flex items-center gap-1">
                          <FaCalendarAlt className="w-3 h-3" />
                          {exp.period}
                        </div>
                        <div className="flex items-center gap-1">
                          <FaMapMarkerAlt className="w-3 h-3" />
                          {exp.location}
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-light-text/80 mb-4">{exp.description}</p>

                    {/* Achievements */}
                    <div>
                      <h5 className="text-sm font-semibold text-teal-accent mb-2">Key Achievements:</h5>
                      <ul className="space-y-1">
                        {exp.achievements.map((achievement, achIndex) => (
                          <li key={achIndex} className="flex items-start gap-2 text-sm text-light-text/70">
                            <div className="w-1 h-1 bg-teal-accent rounded-full mt-2 flex-shrink-0"></div>
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Education Section */}
            <motion.div variants={itemVariants}>
              <div className="flex items-center gap-3 mb-8">
                <FaGraduationCap className="text-2xl text-teal-accent" />
                <h3 className="text-2xl font-heading font-bold text-light-text">
                  Education
                </h3>
              </div>

              <div className="space-y-8">
                {education.map((edu, index) => (
                  <motion.div
                    key={index}
                    className="relative bg-dark-navy/50 border border-teal-accent/20 rounded-xl p-6"
                    variants={itemVariants}
                    whileHover={{ scale: 1.02, y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* Timeline Dot */}
                    <div className="absolute -left-3 top-6 w-6 h-6 bg-teal-accent rounded-full border-4 border-section-bg"></div>
                    
                    {/* Degree & Institution */}
                    <div className="mb-3">
                      <h4 className="text-xl font-heading font-semibold text-light-text mb-1">
                        {edu.degree}
                      </h4>
                      <div className="text-teal-accent font-medium mb-2">{edu.institution}</div>
                      <div className="flex flex-wrap gap-4 text-sm text-light-text/60">
                        <div className="flex items-center gap-1">
                          <FaCalendarAlt className="w-3 h-3" />
                          {edu.period}
                        </div>
                        <div className="flex items-center gap-1">
                          <FaMapMarkerAlt className="w-3 h-3" />
                          {edu.location}
                        </div>
                      </div>
                      <div className="text-sm text-sky-blue mt-1">GPA: {edu.gpa}</div>
                    </div>

                    {/* Relevant Coursework */}
                    <div>
                      <h5 className="text-sm font-semibold text-sky-blue mb-2">Relevant Coursework:</h5>
                      <div className="flex flex-wrap gap-2">
                        {edu.relevant.map((course, courseIndex) => (
                          <span
                            key={courseIndex}
                            className="px-3 py-1 bg-sky-blue/10 border border-sky-blue/30 rounded-full text-xs text-sky-blue"
                          >
                            {course}
                          </span>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Additional Info */}
          <motion.div
            variants={itemVariants}
            className="mt-16 text-center"
          >
            <div className="bg-dark-navy/30 border border-sky-blue/10 rounded-2xl p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-heading font-semibold text-light-text mb-4">
                Ready to Work Together?
              </h3>
              <p className="text-light-text/70 mb-6">
                I'm always open to discussing new opportunities and exciting projects. 
                Let's connect and see how we can create something amazing together!
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <span className="px-4 py-2 bg-sky-blue/10 border border-sky-blue/30 rounded-full text-sky-blue text-sm">
                  Available for Freelance
                </span>
                <span className="px-4 py-2 bg-teal-accent/10 border border-teal-accent/30 rounded-full text-teal-accent text-sm">
                  Open to Full-time
                </span>
                <span className="px-4 py-2 bg-sky-blue/10 border border-sky-blue/30 rounded-full text-sky-blue text-sm">
                  Remote Friendly
                </span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Resume;
