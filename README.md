# Yadav Rana - Portfolio Website

A sleek, modern tech portfolio website built with React, Tailwind CSS, and Framer Motion.

## 🚀 Features

- **Modern Design**: Dark-themed, minimal, professional, and futuristic
- **Responsive**: Fully responsive across mobile, tablet, and desktop
- **Smooth Animations**: Powered by Framer Motion for engaging user experience
- **Interactive Elements**: Custom cursor, hover effects, and scroll animations
- **Contact Form**: Integrated with EmailJS for direct messaging
- **Performance Optimized**: Fast loading and smooth scrolling

## 🎨 Design System

### Color Scheme

- **Background**: #0F172A (dark navy)
- **Primary Accent**: #38BDF8 (sky blue)
- **Highlight**: #14B8A6 (teal)
- **Card Background/Light Text**: #F8FAFC
- **Section Background**: #1E293B

### Typography

- **Headings**: Neue Machina font
- **Body Text**: Helvetica/Arial

## 📱 Sections

1. **Hero Section** - Landing with animated headline and call-to-action buttons
2. **About Me** - Professional bio with personal touch
3. **Skills** - Interactive skill grid with progress bars
4. **Projects** - Featured projects with tech stack and live demos
5. **Resume** - Experience, education, and downloadable resume
6. **Contact** - Contact form and social links
7. **Footer** - Social links and scroll-to-top functionality

## 🛠️ Tech Stack

- **Frontend**: React 19, Vite
- **Styling**: Tailwind CSS 4
- **Animations**: Framer Motion
- **Email Service**: EmailJS
- **Icons**: React Icons
- **Deployment Ready**: Optimized for production

## 🚀 Getting Started

1. **Clone the repository**

   ```bash
   git clone https://github.com/yadavrana/portfolio.git
   cd portfolio
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

## 📧 EmailJS Setup

To enable the contact form functionality:

1. Create an account at [EmailJS](https://www.emailjs.com/)
2. Create an email service and template
3. Update the EmailJS configuration in:
   - `src/components/ContactModal.jsx`
   - `src/components/Contact.jsx`

Replace the placeholder values:

```javascript
const serviceId = "your_service_id";
const templateId = "your_template_id";
const publicKey = "your_public_key";
```

## 🎯 Customization

### Personal Information

Update the following files with your information:

- `src/components/Hero.jsx` - Name and title
- `src/components/About.jsx` - Bio and personal details
- `src/components/Skills.jsx` - Your skills and proficiency levels
- `src/components/Projects.jsx` - Your projects and links
- `src/components/Resume.jsx` - Experience and education
- `src/components/Contact.jsx` - Contact information
- `src/components/Footer.jsx` - Social links

### Resume PDF

Replace `public/resume-yadav-rana.pdf` with your actual resume file.

### Colors and Fonts

Modify `tailwind.config.js` to customize the color scheme and typography.

## 📱 Mobile Optimization

- Responsive design for all screen sizes
- Touch-friendly interactions
- Optimized performance for mobile devices
- Custom cursor disabled on mobile

## 🔧 Performance Features

- Lazy loading for images
- Optimized animations with `prefers-reduced-motion` support
- Efficient bundle size with Vite
- Smooth scrolling and transitions

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio!

## 📞 Contact

- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/yadavrana](https://linkedin.com/in/yadavrana)
- **GitHub**: [github.com/yadavrana](https://github.com/yadavrana)

---

Built with ❤️ using React, Tailwind CSS, and Framer Motion
