import { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import emailjs from '@emailjs/browser';
import { FaEnvelope, FaLinkedin, FaGithub, FaPaperPlane, FaMapMarkerAlt, FaPhone } from 'react-icons/fa';

const Contact = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.2 });
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const contactInfo = [
    {
      icon: FaEnvelope,
      label: "Email",
      value: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    {
      icon: FaLinkedin,
      label: "LinkedIn",
      value: "linkedin.com/in/yadavrana",
      link: "https://linkedin.com/in/yadavrana"
    },
    {
      icon: FaGithub,
      label: "GitHub",
      value: "github.com/yadavrana",
      link: "https://github.com/yadavrana"
    },
    {
      icon: FaMapMarkerAlt,
      label: "Location",
      value: "Available Worldwide",
      link: null
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // EmailJS configuration - Replace with your actual values
      const serviceId = 'your_service_id';
      const templateId = 'your_template_id';
      const publicKey = 'your_public_key';

      const templateParams = {
        from_name: formData.name,
        from_email: formData.email,
        message: formData.message,
        to_name: 'Yadav Rana',
      };

      await emailjs.send(serviceId, templateId, templateParams, publicKey);
      
      setSubmitStatus('success');
      setFormData({ name: '', email: '', message: '' });
      
    } catch (error) {
      console.error('Email send failed:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8 bg-dark-navy">
      <div className="max-w-7xl mx-auto">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Title */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-heading font-bold text-light-text mb-4">
              Get In{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-sky-blue to-teal-accent">
                Touch
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-sky-blue to-teal-accent mx-auto rounded-full mb-6"></div>
            <p className="text-lg text-light-text/70 max-w-2xl mx-auto">
              Have a project in mind or just want to chat? I'd love to hear from you. 
              Let's create something amazing together!
            </p>
          </motion.div>

          {/* Contact Content */}
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div>
                <h3 className="text-2xl font-heading font-semibold text-light-text mb-6">
                  Let's Connect
                </h3>
                <p className="text-light-text/80 mb-8">
                  I'm always excited to work on new projects and collaborate with 
                  talented people. Whether you have a question, a project idea, 
                  or just want to say hello, feel free to reach out!
                </p>
              </div>

              {/* Contact Info Cards */}
              <div className="space-y-4">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center gap-4 p-4 bg-section-bg/50 border border-sky-blue/20 rounded-xl hover:border-sky-blue/40 transition-colors duration-200"
                    whileHover={{ x: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="p-3 bg-sky-blue/20 rounded-lg">
                      <info.icon className="w-5 h-5 text-sky-blue" />
                    </div>
                    <div>
                      <div className="text-sm text-light-text/60">{info.label}</div>
                      {info.link ? (
                        <a
                          href={info.link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-light-text hover:text-sky-blue transition-colors duration-200"
                        >
                          {info.value}
                        </a>
                      ) : (
                        <div className="text-light-text">{info.value}</div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Social Links */}
              <div>
                <h4 className="text-lg font-heading font-semibold text-light-text mb-4">
                  Follow Me
                </h4>
                <div className="flex gap-4">
                  {[
                    { icon: FaGithub, link: "https://github.com/yadavrana", label: "GitHub" },
                    { icon: FaLinkedin, link: "https://linkedin.com/in/yadavrana", label: "LinkedIn" },
                    { icon: FaEnvelope, link: "mailto:<EMAIL>", label: "Email" },
                  ].map((social, index) => (
                    <motion.a
                      key={index}
                      href={social.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-3 bg-sky-blue/20 border border-sky-blue/30 rounded-lg text-sky-blue hover:bg-sky-blue/30 transition-colors duration-200"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      aria-label={social.label}
                    >
                      <social.icon className="w-5 h-5" />
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Contact Form */}
            <motion.div variants={itemVariants}>
              <div className="bg-section-bg/50 border border-sky-blue/20 rounded-2xl p-8">
                <h3 className="text-2xl font-heading font-semibold text-light-text mb-6">
                  Send Message
                </h3>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Name Field */}
                  <div>
                    <label htmlFor="contact-name" className="block text-sm font-medium text-light-text mb-2">
                      Your Name
                    </label>
                    <input
                      type="text"
                      id="contact-name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-dark-navy border border-sky-blue/30 rounded-lg text-light-text placeholder-light-text/50 focus:border-sky-blue focus:ring-2 focus:ring-sky-blue/20 transition-colors duration-200"
                      placeholder="John Doe"
                    />
                  </div>

                  {/* Email Field */}
                  <div>
                    <label htmlFor="contact-email" className="block text-sm font-medium text-light-text mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="contact-email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-dark-navy border border-sky-blue/30 rounded-lg text-light-text placeholder-light-text/50 focus:border-sky-blue focus:ring-2 focus:ring-sky-blue/20 transition-colors duration-200"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  {/* Message Field */}
                  <div>
                    <label htmlFor="contact-message" className="block text-sm font-medium text-light-text mb-2">
                      Message
                    </label>
                    <textarea
                      id="contact-message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 bg-dark-navy border border-sky-blue/30 rounded-lg text-light-text placeholder-light-text/50 focus:border-sky-blue focus:ring-2 focus:ring-sky-blue/20 transition-colors duration-200 resize-none"
                      placeholder="Tell me about your project or just say hello!"
                    />
                  </div>

                  {/* Submit Button */}
                  <motion.button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full px-6 py-4 bg-gradient-to-r from-sky-blue to-teal-accent text-dark-navy font-semibold rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                    whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-dark-navy/30 border-t-dark-navy rounded-full animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <FaPaperPlane className="w-4 h-4" />
                        Send Message
                      </>
                    )}
                  </motion.button>
                </form>

                {/* Status Messages */}
                {submitStatus && (
                  <motion.div
                    className={`mt-6 p-4 rounded-lg text-center ${
                      submitStatus === 'success'
                        ? 'bg-teal-accent/20 text-teal-accent border border-teal-accent/30'
                        : 'bg-red-500/20 text-red-400 border border-red-500/30'
                    }`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    {submitStatus === 'success'
                      ? '✅ Message sent successfully! I\'ll get back to you soon.'
                      : '❌ Failed to send message. Please try again or contact me directly.'}
                  </motion.div>
                )}
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
