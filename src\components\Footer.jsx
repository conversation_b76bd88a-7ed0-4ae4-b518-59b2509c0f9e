import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaGithub, FaLinkedin, FaEnvelope, FaArrowUp, FaHeart } from 'react-icons/fa';

const Footer = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const socialLinks = [
    {
      icon: FaGithub,
      href: "https://github.com/yadavrana",
      label: "GitHub",
      color: "hover:text-gray-400"
    },
    {
      icon: FaLinkedin,
      href: "https://linkedin.com/in/yadavrana",
      label: "LinkedIn",
      color: "hover:text-blue-400"
    },
    {
      icon: FaEnvelope,
      href: "mailto:<EMAIL>",
      label: "Email",
      color: "hover:text-teal-accent"
    }
  ];

  const quickLinks = [
    { name: "Home", href: "#home" },
    { name: "About", href: "#about" },
    { name: "Skills", href: "#skills" },
    { name: "Projects", href: "#projects" },
    { name: "Resume", href: "#resume" },
    { name: "Contact", href: "#contact" }
  ];

  const scrollToSection = (href) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <>
      {/* Scroll to Top Button */}
      <AnimatePresence>
        {showScrollTop && (
          <motion.button
            onClick={scrollToTop}
            className="fixed bottom-8 right-8 z-40 p-3 bg-gradient-to-r from-sky-blue to-teal-accent text-dark-navy rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300"
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            aria-label="Scroll to top"
          >
            <FaArrowUp className="w-5 h-5" />
          </motion.button>
        )}
      </AnimatePresence>

      {/* Footer */}
      <footer className="bg-section-bg border-t border-sky-blue/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Brand Section */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center gap-2">
                <div className="font-heading font-bold text-2xl text-sky-blue">
                  YR
                </div>
                <div className="text-light-text font-heading font-semibold">
                  Yadav Rana
                </div>
              </div>
              <p className="text-light-text/70 text-sm leading-relaxed">
                Full-stack developer passionate about creating elegant, 
                fast, and user-focused web experiences with modern technologies.
              </p>
              <div className="flex items-center gap-2 text-sm text-light-text/60">
                <span>Made with</span>
                <FaHeart className="w-3 h-3 text-red-400 animate-pulse" />
                <span>using React & Tailwind CSS</span>
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h3 className="font-heading font-semibold text-light-text text-lg">
                Quick Links
              </h3>
              <div className="grid grid-cols-2 gap-2">
                {quickLinks.map((link, index) => (
                  <motion.button
                    key={link.name}
                    onClick={() => scrollToSection(link.href)}
                    className="text-left text-light-text/70 hover:text-sky-blue transition-colors duration-200 text-sm py-1"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    {link.name}
                  </motion.button>
                ))}
              </div>
            </motion.div>

            {/* Contact & Social */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="font-heading font-semibold text-light-text text-lg">
                Connect With Me
              </h3>
              <div className="space-y-3">
                <div className="text-light-text/70 text-sm">
                  <div>📧 <EMAIL></div>
                  <div>📍 Available Worldwide</div>
                </div>
                
                {/* Social Links */}
                <div className="flex gap-4">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={social.label}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`p-2 bg-dark-navy/50 border border-sky-blue/30 rounded-lg text-light-text/70 ${social.color} transition-colors duration-200`}
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      aria-label={social.label}
                    >
                      <social.icon className="w-4 h-4" />
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>

          {/* Bottom Section */}
          <motion.div
            className="mt-12 pt-8 border-t border-sky-blue/20 flex flex-col md:flex-row justify-between items-center gap-4"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="text-light-text/60 text-sm text-center md:text-left">
              © {new Date().getFullYear()} Yadav Rana. All rights reserved.
            </div>
            
            <div className="flex items-center gap-6 text-sm text-light-text/60">
              <motion.button
                onClick={() => scrollToSection('#home')}
                className="hover:text-sky-blue transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
              >
                Back to Top
              </motion.button>
              <span>•</span>
              <motion.a
                href="mailto:<EMAIL>"
                className="hover:text-teal-accent transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
              >
                Say Hello
              </motion.a>
            </div>
          </motion.div>
        </div>

        {/* Decorative Bottom Border */}
        <div className="h-1 bg-gradient-to-r from-sky-blue via-teal-accent to-sky-blue"></div>
      </footer>
    </>
  );
};

export default Footer;
