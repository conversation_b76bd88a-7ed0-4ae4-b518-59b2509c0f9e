@import "tailwindcss";

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Helvetica", "Arial", "system-ui", sans-serif;
  background-color: #0f172a;
  color: #f8fafc;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #38bdf8;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #14b8a6;
}

/* Selection styles */
::selection {
  background-color: #38bdf8;
  color: #0f172a;
}

/* Focus styles */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid #38bdf8;
  outline-offset: 2px;
}

/* Smooth transitions for better UX */
button,
a,
input,
textarea {
  transition: all 0.2s ease-in-out;
}

/* Loading animation for images */
img {
  transition: opacity 0.3s ease-in-out;
}

/* Disable cursor on mobile */
@media (max-width: 768px) {
  .cursor-none {
    cursor: auto !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-dark-navy {
    background-color: #000000;
  }
  
  .text-light-text {
    color: #ffffff;
  }
  
  .border-sky-blue\/20 {
    border-color: #38bdf8;
  }
}
