import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { FaGithub, FaExternalLinkAlt, FaReact, FaNodeJs } from 'react-icons/fa';
import { SiMongodb, SiExpress, SiTailwindcss, SiJavascript } from 'react-icons/si';

const Projects = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.2 });

  const projects = [
    {
      id: 1,
      title: "E-Commerce Platform",
      description: "A full-stack e-commerce solution with user authentication, product management, shopping cart, and payment integration.",
      image: "🛒",
      tech: [
        { name: "React", icon: FaReact, color: "text-blue-400" },
        { name: "Node.js", icon: FaNodeJs, color: "text-green-400" },
        { name: "MongoDB", icon: SiMongodb, color: "text-green-500" },
        { name: "Express", icon: SiExpress, color: "text-gray-400" },
      ],
      features: ["User Authentication", "Payment Gateway", "Admin Dashboard", "Responsive Design"],
      github: "https://github.com/yadavrana",
      demo: "https://demo-ecommerce.com",
      status: "Completed"
    },
    {
      id: 2,
      title: "Task Management App",
      description: "A collaborative task management application with real-time updates, team collaboration, and project tracking.",
      image: "📋",
      tech: [
        { name: "React", icon: FaReact, color: "text-blue-400" },
        { name: "Express", icon: SiExpress, color: "text-gray-400" },
        { name: "MongoDB", icon: SiMongodb, color: "text-green-500" },
        { name: "Tailwind", icon: SiTailwindcss, color: "text-cyan-400" },
      ],
      features: ["Real-time Updates", "Team Collaboration", "Drag & Drop", "Progress Tracking"],
      github: "https://github.com/yadavrana",
      demo: "https://demo-taskapp.com",
      status: "In Progress"
    },
    {
      id: 3,
      title: "Weather Dashboard",
      description: "A beautiful weather application with location-based forecasts, interactive maps, and detailed weather analytics.",
      image: "🌤️",
      tech: [
        { name: "JavaScript", icon: SiJavascript, color: "text-yellow-400" },
        { name: "React", icon: FaReact, color: "text-blue-400" },
        { name: "Tailwind", icon: SiTailwindcss, color: "text-cyan-400" },
      ],
      features: ["Location Detection", "5-Day Forecast", "Interactive Maps", "Weather Alerts"],
      github: "https://github.com/yadavrana",
      demo: "https://demo-weather.com",
      status: "Completed"
    },
    {
      id: 4,
      title: "Portfolio Website",
      description: "A modern, responsive portfolio website built with React and Framer Motion, featuring smooth animations and dark theme.",
      image: "💼",
      tech: [
        { name: "React", icon: FaReact, color: "text-blue-400" },
        { name: "Tailwind", icon: SiTailwindcss, color: "text-cyan-400" },
      ],
      features: ["Smooth Animations", "Responsive Design", "Contact Form", "Dark Theme"],
      github: "https://github.com/yadavrana",
      demo: "https://yadavrana.dev",
      status: "Completed"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const projectVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="projects" className="py-20 px-4 sm:px-6 lg:px-8 bg-dark-navy">
      <div className="max-w-7xl mx-auto">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Title */}
          <motion.div variants={projectVariants} className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-heading font-bold text-light-text mb-4">
              Featured{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-sky-blue to-teal-accent">
                Projects
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-sky-blue to-teal-accent mx-auto rounded-full mb-6"></div>
            <p className="text-lg text-light-text/70 max-w-2xl mx-auto">
              Here are some of the projects I've worked on, showcasing my skills in full-stack development
            </p>
          </motion.div>

          {/* Projects Grid */}
          <div className="grid md:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <motion.div
                key={project.id}
                variants={projectVariants}
                className="group bg-section-bg border border-sky-blue/20 rounded-2xl overflow-hidden hover:border-sky-blue/40 transition-all duration-300"
                whileHover={{ y: -10, scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                {/* Project Image/Icon */}
                <div className="relative h-48 bg-gradient-to-br from-sky-blue/10 to-teal-accent/10 flex items-center justify-center overflow-hidden">
                  <div className="text-6xl">{project.image}</div>
                  
                  {/* Status Badge */}
                  <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-medium ${
                    project.status === 'Completed' 
                      ? 'bg-teal-accent/20 text-teal-accent border border-teal-accent/30'
                      : 'bg-sky-blue/20 text-sky-blue border border-sky-blue/30'
                  }`}>
                    {project.status}
                  </div>

                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-dark-navy/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-4">
                    <motion.a
                      href={project.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-3 bg-sky-blue/20 border border-sky-blue/30 rounded-full text-sky-blue hover:bg-sky-blue/30 transition-colors duration-200"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaGithub className="w-5 h-5" />
                    </motion.a>
                    <motion.a
                      href={project.demo}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-3 bg-teal-accent/20 border border-teal-accent/30 rounded-full text-teal-accent hover:bg-teal-accent/30 transition-colors duration-200"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FaExternalLinkAlt className="w-5 h-5" />
                    </motion.a>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  {/* Title */}
                  <h3 className="text-xl font-heading font-semibold text-light-text mb-3 group-hover:text-sky-blue transition-colors duration-200">
                    {project.title}
                  </h3>

                  {/* Description */}
                  <p className="text-light-text/70 mb-4 line-clamp-3">
                    {project.description}
                  </p>

                  {/* Tech Stack */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.tech.map((tech, techIndex) => (
                      <div
                        key={techIndex}
                        className="flex items-center gap-1 px-3 py-1 bg-dark-navy/50 border border-sky-blue/20 rounded-full text-xs"
                      >
                        <tech.icon className={`w-3 h-3 ${tech.color}`} />
                        <span className="text-light-text/80">{tech.name}</span>
                      </div>
                    ))}
                  </div>

                  {/* Features */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-sky-blue mb-2">Key Features:</h4>
                    <div className="grid grid-cols-2 gap-1">
                      {project.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2 text-xs text-light-text/70">
                          <div className="w-1 h-1 bg-teal-accent rounded-full"></div>
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <a
                      href={project.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 border border-sky-blue/30 text-sky-blue rounded-lg hover:bg-sky-blue/10 transition-colors duration-200 text-sm font-medium"
                    >
                      <FaGithub className="w-4 h-4" />
                      Code
                    </a>
                    <a
                      href={project.demo}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-sky-blue to-teal-accent text-dark-navy rounded-lg hover:shadow-lg transition-shadow duration-200 text-sm font-medium"
                    >
                      <FaExternalLinkAlt className="w-4 h-4" />
                      Demo
                    </a>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* View More Projects */}
          <motion.div
            variants={projectVariants}
            className="text-center mt-12"
          >
            <motion.a
              href="https://github.com/yadavrana"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-8 py-3 border-2 border-sky-blue text-sky-blue font-semibold rounded-lg hover:bg-sky-blue hover:text-dark-navy transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FaGithub className="w-5 h-5" />
              View More Projects
            </motion.a>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
